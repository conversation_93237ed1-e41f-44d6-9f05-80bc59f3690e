<!DOCTYPE html>
<html>
<head>
    <title>Test Tabbed IFrame Plugin</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .demo-section { background: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Test Tabbed IFrame Plugin</h1>
    
    <?php
    $base_path = '/var/www/html/med';
    $tests = array();
    
    // Test 1: Check if tabbed iframe CSS is in header
    $header_file = $base_path . '/application/views/layouts/header.php';
    if (file_exists($header_file)) {
        $header_content = file_get_contents($header_file);
        if (strpos($header_content, 'content-tabs') !== false && strpos($header_content, 'tab-nav') !== false) {
            $tests[] = array('status' => 'success', 'message' => 'CSS untuk tabbed iframe ditemukan di header.php');
        } else {
            $tests[] = array('status' => 'error', 'message' => 'CSS untuk tabbed iframe tidak ditemukan di header.php');
        }
    } else {
        $tests[] = array('status' => 'error', 'message' => 'File header.php tidak ditemukan');
    }
    
    // Test 2: Check if tabbed iframe JS exists
    $js_file = $base_path . '/assets/js/tabbed-iframe.js';
    if (file_exists($js_file)) {
        $js_content = file_get_contents($js_file);
        if (strpos($js_content, 'class TabbedIFrame') !== false) {
            $tests[] = array('status' => 'success', 'message' => 'File JavaScript tabbed-iframe.js ditemukan');
        } else {
            $tests[] = array('status' => 'error', 'message' => 'Class TabbedIFrame tidak ditemukan di JavaScript file');
        }
    } else {
        $tests[] = array('status' => 'error', 'message' => 'File tabbed-iframe.js tidak ditemukan');
    }
    
    // Test 3: Check if JS is included in footer
    $footer_file = $base_path . '/application/views/layouts/footer.php';
    if (file_exists($footer_file)) {
        $footer_content = file_get_contents($footer_file);
        if (strpos($footer_content, 'tabbed-iframe.js') !== false) {
            $tests[] = array('status' => 'success', 'message' => 'JavaScript tabbed-iframe.js di-include di footer.php');
        } else {
            $tests[] = array('status' => 'error', 'message' => 'JavaScript tabbed-iframe.js tidak di-include di footer.php');
        }
    } else {
        $tests[] = array('status' => 'error', 'message' => 'File footer.php tidak ditemukan');
    }
    
    // Test 4: Check main tabbed layout
    $main_tabbed_file = $base_path . '/application/views/layouts/main_tabbed.php';
    if (file_exists($main_tabbed_file)) {
        $tests[] = array('status' => 'success', 'message' => 'Layout main_tabbed.php ditemukan');
    } else {
        $tests[] = array('status' => 'error', 'message' => 'Layout main_tabbed.php tidak ditemukan');
    }
    
    // Test 5: Check Main controller
    $main_controller = $base_path . '/application/controllers/Main.php';
    if (file_exists($main_controller)) {
        $controller_content = file_get_contents($main_controller);
        if (strpos($controller_content, 'main_tabbed') !== false) {
            $tests[] = array('status' => 'success', 'message' => 'Controller Main.php ditemukan dengan layout tabbed');
        } else {
            $tests[] = array('status' => 'warning', 'message' => 'Controller Main.php ditemukan tapi tidak menggunakan layout tabbed');
        }
    } else {
        $tests[] = array('status' => 'error', 'message' => 'Controller Main.php tidak ditemukan');
    }
    
    // Test 6: Check sidebar updates
    $sidebar_file = $base_path . '/application/views/layouts/sidebar.php';
    if (file_exists($sidebar_file)) {
        $sidebar_content = file_get_contents($sidebar_file);
        if (strpos($sidebar_content, 'data-tab-title') !== false) {
            $tests[] = array('status' => 'success', 'message' => 'Sidebar sudah diupdate untuk mendukung tab (data-tab-title)');
        } else {
            $tests[] = array('status' => 'warning', 'message' => 'Sidebar belum diupdate untuk mendukung tab');
        }
    } else {
        $tests[] = array('status' => 'error', 'message' => 'File sidebar.php tidak ditemukan');
    }
    
    // Test 7: Check routes configuration
    $routes_file = $base_path . '/application/config/routes.php';
    if (file_exists($routes_file)) {
        $routes_content = file_get_contents($routes_file);
        if (strpos($routes_content, "default_controller'] = 'main'") !== false) {
            $tests[] = array('status' => 'success', 'message' => 'Default controller sudah diset ke main');
        } else {
            $tests[] = array('status' => 'info', 'message' => 'Default controller belum diset ke main');
        }
    }
    
    // Test 8: Check example views
    $views = array(
        'application/views/settings/index.php',
        'application/views/reports/index.php'
    );
    
    foreach ($views as $view) {
        $full_path = $base_path . '/' . $view;
        if (file_exists($full_path)) {
            $tests[] = array('status' => 'success', 'message' => "View $view ditemukan");
        } else {
            $tests[] = array('status' => 'warning', 'message' => "View $view tidak ditemukan");
        }
    }
    
    // Test 9: Check documentation
    $docs = array(
        'README_TabbedIFrame.md'
    );
    
    foreach ($docs as $doc) {
        $full_path = $base_path . '/' . $doc;
        if (file_exists($full_path)) {
            $tests[] = array('status' => 'success', 'message' => "Dokumentasi $doc ditemukan");
        } else {
            $tests[] = array('status' => 'warning', 'message' => "Dokumentasi $doc tidak ditemukan");
        }
    }
    
    // Display results
    $success_count = 0;
    $error_count = 0;
    $warning_count = 0;
    
    foreach ($tests as $test) {
        echo '<div class="test-item ' . $test['status'] . '">';
        echo '<strong>' . strtoupper($test['status']) . ':</strong> ' . $test['message'];
        echo '</div>';
        
        if ($test['status'] == 'success') $success_count++;
        if ($test['status'] == 'error') $error_count++;
        if ($test['status'] == 'warning') $warning_count++;
    }
    
    echo '<hr>';
    echo '<h2>Ringkasan</h2>';
    echo '<p><strong>Total Tests:</strong> ' . count($tests) . '</p>';
    echo '<p class="success"><strong>Berhasil:</strong> ' . $success_count . '</p>';
    echo '<p class="error"><strong>Error:</strong> ' . $error_count . '</p>';
    echo '<p class="warning"><strong>Warning:</strong> ' . $warning_count . '</p>';
    
    if ($error_count == 0) {
        echo '<div class="test-item success">';
        echo '<h3>🎉 Tabbed IFrame Plugin Berhasil Diimplementasikan!</h3>';
        echo '<p>Semua komponen utama sudah tersedia. Anda bisa mengakses:</p>';
        echo '<ul>';
        echo '<li><a href="' . (isset($_SERVER['HTTP_HOST']) ? 'http://' . $_SERVER['HTTP_HOST'] . '/med' : '/med') . '" target="_blank">Halaman Utama dengan Tab Interface</a></li>';
        echo '<li><a href="' . (isset($_SERVER['HTTP_HOST']) ? 'http://' . $_SERVER['HTTP_HOST'] . '/med/main/dashboard' : '/med/main/dashboard') . '" target="_blank">Dashboard (untuk iframe)</a></li>';
        echo '<li><a href="' . (isset($_SERVER['HTTP_HOST']) ? 'http://' . $_SERVER['HTTP_HOST'] . '/med/main/users' : '/med/main/users') . '" target="_blank">Users Management (untuk iframe)</a></li>';
        echo '</ul>';
        echo '</div>';
    } else {
        echo '<div class="test-item error">';
        echo '<h3>❌ Setup Belum Lengkap</h3>';
        echo '<p>Masih ada ' . $error_count . ' error yang perlu diperbaiki.</p>';
        echo '</div>';
    }
    ?>
    
    <div class="demo-section">
        <h2>🚀 Demo Fitur Tabbed IFrame</h2>
        <p>Setelah mengakses halaman utama, Anda dapat:</p>
        <ol>
            <li><strong>Klik menu di sidebar</strong> - Akan membuka halaman dalam tab baru</li>
            <li><strong>Gunakan kontrol tab</strong>:
                <ul>
                    <li>Klik tab untuk berpindah</li>
                    <li>Klik × untuk menutup tab</li>
                    <li>Gunakan tombol refresh, close others, close all</li>
                </ul>
            </li>
            <li><strong>Keyboard shortcuts</strong>:
                <ul>
                    <li>Ctrl + W - Tutup tab aktif</li>
                    <li>Ctrl + R - Refresh tab aktif</li>
                </ul>
            </li>
            <li><strong>JavaScript API</strong>:
                <ul>
                    <li><code>openTab('Title', 'URL')</code> - Buka tab baru</li>
                    <li><code>closeTab('tab-id')</code> - Tutup tab</li>
                </ul>
            </li>
        </ol>
    </div>
    
    <hr>
    <h2>Fitur yang Diimplementasikan</h2>
    <ul>
        <li>✅ <strong>Multiple Tabs</strong> - Buka beberapa halaman dalam tab</li>
        <li>✅ <strong>IFrame Loading</strong> - Setiap tab memuat halaman dalam iframe</li>
        <li>✅ <strong>Tab Controls</strong> - Refresh, close others, close all</li>
        <li>✅ <strong>Keyboard Shortcuts</strong> - Ctrl+W, Ctrl+R</li>
        <li>✅ <strong>Auto Title Detection</strong> - Otomatis mengambil title dari halaman</li>
        <li>✅ <strong>Loading Indicator</strong> - Indikator loading saat memuat</li>
        <li>✅ <strong>Error Handling</strong> - Menangani error loading halaman</li>
        <li>✅ <strong>Responsive Design</strong> - Mendukung tampilan mobile</li>
        <li>✅ <strong>Dark Mode Support</strong> - Mendukung tema gelap</li>
        <li>✅ <strong>Max Tabs Limit</strong> - Batasi jumlah maksimal tab (default: 10)</li>
        <li>✅ <strong>Sidebar Integration</strong> - Terintegrasi dengan menu sidebar</li>
        <li>✅ <strong>JavaScript API</strong> - API untuk kontrol tab dari JavaScript</li>
    </ul>
    
    <hr>
    <h2>Struktur File yang Dibuat</h2>
    <pre>
assets/
├── js/
│   └── tabbed-iframe.js          # JavaScript untuk tab functionality
application/
├── controllers/
│   └── Main.php                  # Controller untuk halaman dengan tab
├── views/
│   ├── layouts/
│   │   └── main_tabbed.php      # Layout khusus untuk tab interface
│   ├── settings/
│   │   └── index.php            # Halaman settings
│   └── reports/
│       └── index.php            # Halaman reports
README_TabbedIFrame.md           # Dokumentasi lengkap plugin
    </pre>
    
    <hr>
    <p><strong>Dokumentasi lengkap:</strong> Baca file <code>README_TabbedIFrame.md</code> untuk panduan detail penggunaan dan kustomisasi.</p>
</body>
</html>
