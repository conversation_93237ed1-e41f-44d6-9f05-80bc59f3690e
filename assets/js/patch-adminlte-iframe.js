// Patch AdminLTE IFrame plugin to prevent auto-initialization if no [data-widget="iframe"] exists
(function() {
    // Wait until AdminLTE is loaded
    function patchIFrame() {
        var $ = window.jQuery;
        var IFrame = $.fn.IFrame && $.fn.IFrame.Constructor;
        if (IFrame && IFrame._jQueryInterface) {
            var orig = IFrame._jQueryInterface;
            IFrame._jQueryInterface = function(config) {
                if ($('[data-widget="iframe"]').length > 0) {
                    return orig.call(this, config);
                }
                // Do nothing if no iframe widget
                return this;
            };
            $.fn.IFrame = IFrame._jQueryInterface;
        }
    }
    if (window.jQuery) {
        patchIFrame();
    } else {
        document.addEventListener('DOMContentLoaded', patchIFrame);
    }
})();
