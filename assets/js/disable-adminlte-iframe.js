/**
 * Disable AdminLTE IFrame auto-initialization when using custom tabbed iframe
 * This prevents conflicts between AdminLTE IFrame and custom tabbed iframe
 */
(function() {
    'use strict';
    
    // Store original IFrame._jQueryInterface
    if (typeof IFrame !== 'undefined' && IFrame._jQueryInterface) {
        const originalJQueryInterface = IFrame._jQueryInterface;
        
        // Override to prevent auto-initialization
        IFrame._jQueryInterface = function(config) {
            // Only initialize if data-widget="iframe" elements exist
            if ($('[data-widget="iframe"]').length > 0) {
                return originalJQueryInterface.call(this, config);
            }
            // Otherwise, do nothing - prevent the error
            return this;
        };
        
        // Also override the jQuery plugin
        if ($.fn && $.fn.IFrame) {
            $.fn.IFrame = IFrame._jQueryInterface;
        }
    }
    
    // Prevent the window load event from triggering IFrame initialization
    $(window).off('load.IFrame');
})();
