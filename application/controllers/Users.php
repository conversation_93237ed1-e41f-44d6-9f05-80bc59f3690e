<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Users extends MY_Controller {

    public function __construct()
    {
        parent::__construct();
    }

    public function index()
    {
        // Set page title dan breadcrumbs
        $this->set_title('Users Management', 'Users');
        $this->set_breadcrumbs(array(
            array('title' => 'Home', 'url' => base_url()),
            array('title' => 'Master Data'),
            array('title' => 'Users')
        ));

        // Add DataTables CSS dan JS
        $this->add_css(base_url('vendor/almasaeed2010/adminlte/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css'));
        $this->add_css(base_url('vendor/almasaeed2010/adminlte/plugins/datatables-responsive/css/responsive.bootstrap4.min.css'));
        $this->add_css(base_url('vendor/almasaeed2010/adminlte/plugins/datatables-buttons/css/buttons.bootstrap4.min.css'));
        
        $this->add_js(base_url('vendor/almasaeed2010/adminlte/plugins/datatables/jquery.dataTables.min.js'));
        $this->add_js(base_url('vendor/almasaeed2010/adminlte/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js'));
        $this->add_js(base_url('vendor/almasaeed2010/adminlte/plugins/datatables-responsive/js/dataTables.responsive.min.js'));
        $this->add_js(base_url('vendor/almasaeed2010/adminlte/plugins/datatables-responsive/js/responsive.bootstrap4.min.js'));

        // Add page script untuk DataTables
        $script = "
        $(function () {
            $('#usersTable').DataTable({
                'paging': true,
                'lengthChange': false,
                'searching': true,
                'ordering': true,
                'info': true,
                'autoWidth': false,
                'responsive': true,
            });
        });
        ";
        $this->add_script($script);

        // Sample data
        $data['users'] = array(
            array('id' => 1, 'name' => 'John Doe', 'email' => '<EMAIL>', 'role' => 'Admin', 'status' => 'Active'),
            array('id' => 2, 'name' => 'Jane Smith', 'email' => '<EMAIL>', 'role' => 'User', 'status' => 'Active'),
            array('id' => 3, 'name' => 'Bob Johnson', 'email' => '<EMAIL>', 'role' => 'User', 'status' => 'Inactive'),
        );

        // Render view
        $this->render('users/index', $data);
    }

    public function create()
    {
        // Set page title dan breadcrumbs
        $this->set_title('Add New User', 'Add User');
        $this->set_breadcrumbs(array(
            array('title' => 'Home', 'url' => base_url()),
            array('title' => 'Master Data'),
            array('title' => 'Users', 'url' => base_url('users')),
            array('title' => 'Add User')
        ));

        if ($this->input->post()) {
            // Process form submission
            $this->set_message('success', 'User berhasil ditambahkan!');
            $this->redirect_to('users');
        }

        // Render view
        $this->render('users/create');
    }

    public function edit($id)
    {
        // Set page title dan breadcrumbs
        $this->set_title('Edit User', 'Edit User');
        $this->set_breadcrumbs(array(
            array('title' => 'Home', 'url' => base_url()),
            array('title' => 'Master Data'),
            array('title' => 'Users', 'url' => base_url('users')),
            array('title' => 'Edit User')
        ));

        if ($this->input->post()) {
            // Process form submission
            $this->set_message('success', 'User berhasil diupdate!');
            $this->redirect_to('users');
        }

        // Sample data
        $data['user'] = array(
            'id' => $id,
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'role' => 'Admin'
        );

        // Render view
        $this->render('users/edit', $data);
    }

    public function delete($id)
    {
        // Process delete
        $this->set_message('success', 'User berhasil dihapus!');
        $this->redirect_to('users');
    }
}
