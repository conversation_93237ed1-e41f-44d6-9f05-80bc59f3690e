<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends MY_Controller {

    public function __construct()
    {
        parent::__construct();
    }

    public function index()
    {
        // Set page title dan breadcrumbs
        $this->set_title('Dashboard');
        $this->set_breadcrumbs(array(
            array('title' => 'Home', 'url' => base_url()),
            array('title' => 'Dashboard')
        ));

        // Add page specific script untuk dashboard charts
        $dashboard_script = "
        // Dashboard charts initialization
        $(function () {
            'use strict'

            // Make the dashboard widgets sortable Using jquery UI
            $('.connectedSortable').sortable({
                placeholder: 'sort-highlight',
                connectWith: '.connectedSortable',
                handle: '.card-header, .nav-tabs',
                forcePlaceholderSize: true,
                zIndex: 999999
            })
            $('.connectedSortable .card-header').css('cursor', 'move')

            // jQuery UI sortable for the todo list
            $('.todo-list').sortable({
                placeholder: 'sort-highlight',
                handle: '.handle',
                forcePlaceholderSize: true,
                zIndex: 999999
            })
        })
        ";

        $this->add_script($dashboard_script);

        // Render view
        $this->render('dashboard');
    }
}
