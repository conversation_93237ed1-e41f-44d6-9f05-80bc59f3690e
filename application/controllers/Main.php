<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Main extends MY_Controller {

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Main page with tabbed interface
     */
    public function index()
    {
        // Set page title
        $this->set_title('Dashboard - Tabbed Interface');
        
        // Load tabbed layout (without content)
        $this->load->view('layouts/main_tabbed');
    }

    /**
     * Dashboard content for iframe
     */
    public function dashboard()
    {
        // Set page title dan breadcrumbs
        $this->set_title('Dashboard');
        $this->set_breadcrumbs(array(
            array('title' => 'Home', 'url' => base_url()),
            array('title' => 'Dashboard')
        ));

        // Add page specific script untuk dashboard charts
        $dashboard_script = "
        // Dashboard charts initialization
        $(function () {
            'use strict'

            // Make the dashboard widgets sortable Using jquery UI
            $('.connectedSortable').sortable({
                placeholder: 'sort-highlight',
                connectWith: '.connectedSortable',
                handle: '.card-header, .nav-tabs',
                forcePlaceholderSize: true,
                zIndex: 999999
            })
            $('.connectedSortable .card-header').css('cursor', 'move')

            // jQuery UI sortable for the todo list
            $('.todo-list').sortable({
                placeholder: 'sort-highlight',
                handle: '.handle',
                forcePlaceholderSize: true,
                zIndex: 999999
            })
        })
        ";
        
        $this->add_script($dashboard_script);

        // Render view with regular layout (for iframe)
        $this->render('dashboard');
    }

    /**
     * Users management for iframe
     */
    public function users()
    {
        // Set page title dan breadcrumbs
        $this->set_title('Users Management', 'Users');
        $this->set_breadcrumbs(array(
            array('title' => 'Home', 'url' => base_url('main/dashboard')),
            array('title' => 'Master Data'),
            array('title' => 'Users')
        ));

        // Add DataTables CSS dan JS
        $this->add_css(base_url('vendor/almasaeed2010/adminlte/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css'));
        $this->add_css(base_url('vendor/almasaeed2010/adminlte/plugins/datatables-responsive/css/responsive.bootstrap4.min.css'));
        
        $this->add_js(base_url('vendor/almasaeed2010/adminlte/plugins/datatables/jquery.dataTables.min.js'));
        $this->add_js(base_url('vendor/almasaeed2010/adminlte/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js'));
        $this->add_js(base_url('vendor/almasaeed2010/adminlte/plugins/datatables-responsive/js/dataTables.responsive.min.js'));
        $this->add_js(base_url('vendor/almasaeed2010/adminlte/plugins/datatables-responsive/js/responsive.bootstrap4.min.js'));

        // Add page script untuk DataTables
        $script = "
        $(function () {
            $('#usersTable').DataTable({
                'paging': true,
                'lengthChange': false,
                'searching': true,
                'ordering': true,
                'info': true,
                'autoWidth': false,
                'responsive': true,
            });
        });
        ";
        $this->add_script($script);

        // Sample data
        $data['users'] = array(
            array('id' => 1, 'name' => 'John Doe', 'email' => '<EMAIL>', 'role' => 'Admin', 'status' => 'Active'),
            array('id' => 2, 'name' => 'Jane Smith', 'email' => '<EMAIL>', 'role' => 'User', 'status' => 'Active'),
            array('id' => 3, 'name' => 'Bob Johnson', 'email' => '<EMAIL>', 'role' => 'User', 'status' => 'Inactive'),
            array('id' => 4, 'name' => 'Alice Brown', 'email' => '<EMAIL>', 'role' => 'User', 'status' => 'Active'),
            array('id' => 5, 'name' => 'Charlie Wilson', 'email' => '<EMAIL>', 'role' => 'Admin', 'status' => 'Active'),
        );

        // Render view
        $this->render('users/index', $data);
    }

    /**
     * Add user form for iframe
     */
    public function users_create()
    {
        // Set page title dan breadcrumbs
        $this->set_title('Add New User', 'Add User');
        $this->set_breadcrumbs(array(
            array('title' => 'Home', 'url' => base_url('main/dashboard')),
            array('title' => 'Master Data'),
            array('title' => 'Users', 'url' => base_url('main/users')),
            array('title' => 'Add User')
        ));

        if ($this->input->post()) {
            // Process form submission
            $this->set_message('success', 'User berhasil ditambahkan!');
            $this->redirect_to('main/users');
        }

        // Render view
        $this->render('users/create');
    }

    /**
     * Settings page for iframe
     */
    public function settings()
    {
        // Set page title dan breadcrumbs
        $this->set_title('Settings');
        $this->set_breadcrumbs(array(
            array('title' => 'Home', 'url' => base_url('main/dashboard')),
            array('title' => 'Settings')
        ));

        // Render view
        $this->render('settings/index');
    }

    /**
     * Reports page for iframe
     */
    public function reports()
    {
        // Set page title dan breadcrumbs
        $this->set_title('Reports');
        $this->set_breadcrumbs(array(
            array('title' => 'Home', 'url' => base_url('main/dashboard')),
            array('title' => 'Reports')
        ));

        // Add Chart.js
        $this->add_js(base_url('vendor/almasaeed2010/adminlte/plugins/chart.js/Chart.min.js'));

        // Add chart script
        $chart_script = "
        $(function () {
            // Sample chart
            var ctx = document.getElementById('salesChart');
            if (ctx) {
                var salesChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                        datasets: [{
                            label: 'Sales',
                            data: [12, 19, 3, 5, 2, 3],
                            borderColor: 'rgb(75, 192, 192)',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        });
        ";
        $this->add_script($chart_script);

        // Render view
        $this->render('reports/index');
    }

    /**
     * Demo page for tabbed iframe
     */
    public function demo()
    {
        // Set page title dan breadcrumbs
        $this->set_title('Demo Tabbed IFrame');
        $this->set_breadcrumbs(array(
            array('title' => 'Home', 'url' => base_url('main/dashboard')),
            array('title' => 'Demo Tabbed IFrame')
        ));

        // Render view
        $this->render('demo_tabs');
    }
}
