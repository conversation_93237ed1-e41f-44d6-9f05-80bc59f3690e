<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Base Controller untuk AdminLTE Layout
 * 
 * Semua controller yang menggunakan AdminLTE layout sebaiknya extend dari class ini
 */
class MY_Controller extends CI_Controller {

    protected $data = array();

    public function __construct()
    {
        parent::__construct();
        
        // Load libraries yang sering digunakan
        $this->load->library('session');
        $this->load->helper(array('url', 'form'));
        
        // Set default data
        $this->data['title'] = 'AdminLTE 3';
        $this->data['page_title'] = 'Dashboard';
        $this->data['breadcrumbs'] = array();
        $this->data['css_files'] = array();
        $this->data['js_files'] = array();
        $this->data['page_script'] = '';
    }

    /**
     * Load view dengan AdminLTE layout
     * 
     * @param string $view Nama view file
     * @param array $data Data yang akan dikirim ke view
     * @param boolean $return Apakah return string atau langsung output
     */
    protected function render($view, $data = array(), $return = FALSE)
    {
        // Merge data
        $data = array_merge($this->data, $data);
        
        // Load content view ke dalam variable
        $data['content'] = $this->load->view($view, $data, TRUE);
        
        // Load main layout
        return $this->load->view('layouts/main', $data, $return);
    }

    /**
     * Set page title
     * 
     * @param string $title Page title
     * @param string $page_title Content header title (optional)
     */
    protected function set_title($title, $page_title = null)
    {
        $this->data['title'] = $title;
        $this->data['page_title'] = $page_title ? $page_title : $title;
    }

    /**
     * Set breadcrumbs
     * 
     * @param array $breadcrumbs Array of breadcrumbs
     * Format: array(
     *     array('title' => 'Home', 'url' => base_url()),
     *     array('title' => 'Current Page')
     * )
     */
    protected function set_breadcrumbs($breadcrumbs)
    {
        $this->data['breadcrumbs'] = $breadcrumbs;
    }

    /**
     * Add CSS file
     * 
     * @param string $css_file CSS file URL
     */
    protected function add_css($css_file)
    {
        $this->data['css_files'][] = $css_file;
    }

    /**
     * Add JS file
     * 
     * @param string $js_file JS file URL
     */
    protected function add_js($js_file)
    {
        $this->data['js_files'][] = $js_file;
    }

    /**
     * Add page specific script
     * 
     * @param string $script JavaScript code
     */
    protected function add_script($script)
    {
        $this->data['page_script'] .= $script . "\n";
    }

    /**
     * Set flash message
     * 
     * @param string $type Message type (success, error, warning, info)
     * @param string $message Message content
     */
    protected function set_message($type, $message)
    {
        $this->session->set_flashdata($type, $message);
    }

    /**
     * Redirect dengan base_url
     * 
     * @param string $uri URI to redirect
     * @param string $method Redirect method
     * @param int $code HTTP status code
     */
    protected function redirect_to($uri = '', $method = 'auto', $code = NULL)
    {
        redirect(base_url($uri), $method, $code);
    }
}
