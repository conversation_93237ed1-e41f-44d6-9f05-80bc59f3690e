<!-- Demo Tabbed IFrame -->
<div class="row">
    <div class="col-12">
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-window-restore mr-1"></i>
                    Demo Tabbed IFrame Plugin
                </h3>
            </div>
            <div class="card-body">
                <p>Halaman ini mendemonstrasikan penggunaan Tabbed IFrame Plugin. Gunakan tombol di bawah untuk membuka tab baru atau mengelola tab yang ada.</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <h5>Buka Tab Baru</h5>
                        <div class="btn-group-vertical w-100 mb-3">
                            <button class="btn btn-primary mb-2" onclick="openTab('Google', 'https://google.com')">
                                <i class="fab fa-google mr-1"></i> Buka Google
                            </button>
                            <button class="btn btn-success mb-2" onclick="openTab('GitHub', 'https://github.com')">
                                <i class="fab fa-github mr-1"></i> Buka GitHub
                            </button>
                            <button class="btn btn-info mb-2" onclick="openTab('AdminLTE Demo', 'https://adminlte.io/themes/v3/index.html')">
                                <i class="fas fa-desktop mr-1"></i> AdminLTE Demo
                            </button>
                            <button class="btn btn-warning mb-2" onclick="openTab('Bootstrap', 'https://getbootstrap.com')">
                                <i class="fab fa-bootstrap mr-1"></i> Bootstrap
                            </button>
                        </div>
                        
                        <h5>Tab Internal</h5>
                        <div class="btn-group-vertical w-100 mb-3">
                            <button class="btn btn-secondary mb-2" onclick="openTab('Dashboard', '<?= base_url('main/dashboard') ?>')">
                                <i class="fas fa-tachometer-alt mr-1"></i> Dashboard
                            </button>
                            <button class="btn btn-secondary mb-2" onclick="openTab('Users', '<?= base_url('main/users') ?>')">
                                <i class="fas fa-users mr-1"></i> Users Management
                            </button>
                            <button class="btn btn-secondary mb-2" onclick="openTab('Settings', '<?= base_url('main/settings') ?>')">
                                <i class="fas fa-cogs mr-1"></i> Settings
                            </button>
                            <button class="btn btn-secondary mb-2" onclick="openTab('Reports', '<?= base_url('main/reports') ?>')">
                                <i class="fas fa-chart-pie mr-1"></i> Reports
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h5>Kelola Tab</h5>
                        <div class="btn-group-vertical w-100 mb-3">
                            <button class="btn btn-outline-primary mb-2" onclick="tabbedIFrame.refreshCurrentTab()">
                                <i class="fas fa-sync-alt mr-1"></i> Refresh Tab Aktif
                            </button>
                            <button class="btn btn-outline-warning mb-2" onclick="tabbedIFrame.closeOtherTabs()">
                                <i class="fas fa-times-circle mr-1"></i> Tutup Tab Lain
                            </button>
                            <button class="btn btn-outline-danger mb-2" onclick="tabbedIFrame.closeAllTabs()">
                                <i class="fas fa-window-close mr-1"></i> Tutup Semua Tab
                            </button>
                            <button class="btn btn-outline-info mb-2" onclick="showTabInfo()">
                                <i class="fas fa-info-circle mr-1"></i> Info Tab
                            </button>
                        </div>
                        
                        <h5>Custom Tab</h5>
                        <div class="form-group">
                            <label for="customTitle">Title:</label>
                            <input type="text" class="form-control" id="customTitle" placeholder="Enter tab title" value="Custom Page">
                        </div>
                        <div class="form-group">
                            <label for="customUrl">URL:</label>
                            <input type="url" class="form-control" id="customUrl" placeholder="Enter URL" value="https://example.com">
                        </div>
                        <button class="btn btn-primary btn-block" onclick="openCustomTab()">
                            <i class="fas fa-plus mr-1"></i> Buka Tab Custom
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Info Cards -->
<div class="row">
    <div class="col-md-4">
        <div class="info-box">
            <span class="info-box-icon bg-info"><i class="fas fa-window-restore"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Total Tabs</span>
                <span class="info-box-number" id="totalTabs">1</span>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="info-box">
            <span class="info-box-icon bg-success"><i class="fas fa-check-circle"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Active Tab</span>
                <span class="info-box-number" id="activeTab">Dashboard</span>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="info-box">
            <span class="info-box-icon bg-warning"><i class="fas fa-limit"></i></span>
            <div class="info-box-content">
                <span class="info-box-text">Max Tabs</span>
                <span class="info-box-number">10</span>
            </div>
        </div>
    </div>
</div>

<!-- Keyboard Shortcuts -->
<div class="row">
    <div class="col-12">
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-keyboard mr-1"></i>
                    Keyboard Shortcuts
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Shortcut</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><kbd>Ctrl</kbd> + <kbd>W</kbd></td>
                                    <td>Tutup tab aktif</td>
                                </tr>
                                <tr>
                                    <td><kbd>Ctrl</kbd> + <kbd>R</kbd></td>
                                    <td>Refresh tab aktif</td>
                                </tr>
                                <tr>
                                    <td><kbd>Ctrl</kbd> + <kbd>T</kbd></td>
                                    <td>Reserved (future use)</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>JavaScript API:</h6>
                        <pre><code>// Buka tab baru
openTab('Title', 'URL', 'id');

// Tutup tab
closeTab('tab-id');

// Refresh tab
tabbedIFrame.refreshTab('tab-id');

// Info tab
console.log(tabbedIFrame.tabs);</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Custom functions for demo
function openCustomTab() {
    const title = document.getElementById('customTitle').value || 'Custom Page';
    const url = document.getElementById('customUrl').value || 'https://example.com';
    
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
        alert('Please enter a valid URL starting with http:// or https://');
        return;
    }
    
    openTab(title, url);
}

function showTabInfo() {
    if (typeof tabbedIFrame !== 'undefined') {
        const info = {
            totalTabs: tabbedIFrame.tabs.length,
            activeTab: tabbedIFrame.activeTabId,
            maxTabs: tabbedIFrame.options.maxTabs,
            tabs: tabbedIFrame.tabs.map(tab => ({
                id: tab.id,
                title: tab.title,
                url: tab.url,
                loaded: tab.loaded
            }))
        };
        
        console.log('Tab Info:', info);
        alert('Tab info logged to console. Check browser developer tools.');
        
        // Update info boxes
        document.getElementById('totalTabs').textContent = info.totalTabs;
        const activeTabObj = tabbedIFrame.tabs.find(tab => tab.id === info.activeTab);
        document.getElementById('activeTab').textContent = activeTabObj ? activeTabObj.title : 'Unknown';
    } else {
        alert('Tabbed IFrame not initialized');
    }
}

// Update info periodically
setInterval(function() {
    if (typeof tabbedIFrame !== 'undefined') {
        document.getElementById('totalTabs').textContent = tabbedIFrame.tabs.length;
        const activeTabObj = tabbedIFrame.tabs.find(tab => tab.id === tabbedIFrame.activeTabId);
        document.getElementById('activeTab').textContent = activeTabObj ? activeTabObj.title : 'Unknown';
    }
}, 2000);

// Demo notification
$(document).ready(function() {
    setTimeout(function() {
        if (typeof showNotification === 'function') {
            showNotification('Demo Tabbed IFrame siap digunakan! Klik tombol untuk membuka tab baru.', 'info', 5000);
        }
    }, 1000);
});
</script>
