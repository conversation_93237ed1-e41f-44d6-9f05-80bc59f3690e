<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?= isset($title) ? $title : 'Dashboard' ?> | AdminLTE 3</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="<?= base_url('vendor/almasaeed2010/adminlte/plugins/fontawesome-free/css/all.min.css') ?>">
    <!-- Ionicons -->
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet" href="<?= base_url('vendor/almasaeed2010/adminlte/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css') ?>">
    <!-- iCheck -->
    <link rel="stylesheet" href="<?= base_url('vendor/almasaeed2010/adminlte/plugins/icheck-bootstrap/icheck-bootstrap.min.css') ?>">
    <!-- JQVMap -->
    <link rel="stylesheet" href="<?= base_url('vendor/almasaeed2010/adminlte/plugins/jqvmap/jqvmap.min.css') ?>">
    <!-- Theme style -->
    <link rel="stylesheet" href="<?= base_url('vendor/almasaeed2010/adminlte/dist/css/adminlte.min.css') ?>">
    <!-- overlayScrollbars -->
    <link rel="stylesheet" href="<?= base_url('vendor/almasaeed2010/adminlte/plugins/overlayScrollbars/css/OverlayScrollbars.min.css') ?>">
    <!-- Daterange picker -->
    <link rel="stylesheet" href="<?= base_url('vendor/almasaeed2010/adminlte/plugins/daterangepicker/daterangepicker.css') ?>">
    <!-- summernote -->
    <link rel="stylesheet" href="<?= base_url('vendor/almasaeed2010/adminlte/plugins/summernote/summernote-bs4.min.css') ?>">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?= base_url('assets/css/custom.css') ?>">

    <!-- Tabbed IFrame Plugin CSS -->
    <style>
        /* Disable AdminLTE default iframe styles that conflict */
        .content-wrapper.iframe-mode {
            overflow: visible !important;
        }

        /* Reset any conflicting styles */
        .content-wrapper .content-tabs {
            position: static !important;
            transform: none !important;
        }

        /* Tab Container - Fixed positioning and z-index */
        .content-tabs {
            background: #fff;
            border-bottom: 1px solid #dee2e6;
            padding: 0;
            margin: 0;
            position: relative;
            z-index: 1050;
            width: 100%;
            box-sizing: border-box;
        }

        .tab-nav {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 0;
            margin: 0;
            overflow-x: auto;
            white-space: nowrap;
            min-height: 40px;
            box-sizing: border-box;
        }

        .tab-list {
            display: flex;
            flex: 1;
            overflow-x: auto;
            scrollbar-width: thin;
        }

        .tab-list::-webkit-scrollbar {
            height: 4px;
        }

        .tab-list::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .tab-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }

        .tab-item {
            display: inline-flex;
            align-items: center;
            padding: 8px 12px;
            background: #e9ecef;
            border: 1px solid #dee2e6;
            border-bottom: none;
            margin-right: 2px;
            cursor: pointer;
            font-size: 13px;
            max-width: 200px;
            min-width: 120px;
            position: relative;
            transition: all 0.2s;
            flex-shrink: 0;
            box-sizing: border-box;
        }

        .tab-item:hover {
            background: #f8f9fa;
        }

        .tab-item.active {
            background: #fff;
            border-bottom: 1px solid #fff;
            margin-bottom: -1px;
            z-index: 1051;
        }

        .tab-title {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 5px;
        }

        .tab-close {
            color: #6c757d;
            cursor: pointer;
            padding: 2px 4px;
            border-radius: 2px;
            font-size: 12px;
            line-height: 1;
            flex-shrink: 0;
        }

        .tab-close:hover {
            background: #dc3545;
            color: #fff;
        }

        .tab-controls {
            display: flex;
            align-items: center;
            padding: 5px 10px;
            background: #f8f9fa;
            border-left: 1px solid #dee2e6;
            flex-shrink: 0;
        }

        .tab-control-btn {
            padding: 4px 8px;
            margin: 0 2px;
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            color: #495057;
        }

        .tab-control-btn:hover {
            background: #e9ecef;
        }

        .tab-content-container {
            position: relative;
            height: calc(100vh - 200px);
            overflow: hidden;
            background: #fff;
            width: 100%;
            box-sizing: border-box;
        }

        .tab-pane {
            display: none;
            width: 100%;
            height: 100%;
            border: none;
            background: #fff;
            position: absolute;
            top: 0;
            left: 0;
            box-sizing: border-box;
        }

        .tab-pane.active {
            display: block;
            position: relative;
        }

        .tab-pane iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: #fff;
            display: block;
        }

        /* Loading indicator */
        .tab-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #6c757d;
        }

        .tab-loading .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .tab-item {
                min-width: 100px;
                max-width: 150px;
                padding: 6px 8px;
                font-size: 12px;
            }

            .tab-controls {
                padding: 3px 6px;
            }

            .tab-control-btn {
                padding: 3px 6px;
                font-size: 11px;
            }
        }

        /* Dark mode support */
        .dark-mode .content-tabs,
        .dark-mode .tab-nav {
            background: #343a40;
            border-color: #495057;
        }

        .dark-mode .tab-item {
            background: #495057;
            border-color: #6c757d;
            color: #fff;
        }

        .dark-mode .tab-item.active {
            background: #343a40;
            border-bottom-color: #343a40;
        }

        .dark-mode .tab-controls {
            background: #495057;
            border-color: #6c757d;
        }

        .dark-mode .tab-control-btn {
            background: #343a40;
            border-color: #6c757d;
            color: #fff;
        }

        .dark-mode .tab-loading {
            background: rgba(52, 58, 64, 0.9);
            color: #fff;
        }

        /* Fix for overlapping content */
        .content-wrapper.tab-active {
            position: relative;
            z-index: 1000;
        }

        /* Prevent AdminLTE iframe conflicts */
        .content-wrapper:not(.tab-active) .content-tabs {
            display: none;
        }

        /* Ensure proper stacking order */
        .main-sidebar {
            z-index: 1040;
        }

        .main-header {
            z-index: 1030;
        }

        /* Fix for nested content */
        .tab-pane iframe {
            pointer-events: auto;
            position: relative;
        }

        /* Prevent body scroll when tabs are active */
        body.tab-mode {
            overflow: hidden;
        }

        /* Better loading indicator */
        .tab-loading {
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* Mobile responsive improvements */
        @media (max-width: 576px) {
            .tab-content-container {
                height: calc(100vh - 140px);
            }

            .tab-item {
                min-width: 70px;
                max-width: 100px;
                padding: 5px 6px;
                font-size: 11px;
            }

            .tab-title {
                margin-right: 3px;
            }

            .tab-close {
                padding: 1px 3px;
                font-size: 10px;
            }

            .tab-nav {
                min-height: 35px;
            }
        }
    </style>

    <?php if(isset($css_files)): ?>
        <?php foreach($css_files as $css): ?>
            <link rel="stylesheet" href="<?= $css ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

    <!-- Preloader -->
    <div class="preloader flex-column justify-content-center align-items-center">
        <img class="animation__shake" src="<?= base_url('vendor/almasaeed2010/adminlte/dist/img/AdminLTELogo.png') ?>" alt="AdminLTELogo" height="60" width="60">
    </div>

    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
        <!-- Left navbar links -->
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
            </li>
            <li class="nav-item d-none d-sm-inline-block">
                <a href="<?= base_url() ?>" class="nav-link">Home</a>
            </li>
            <li class="nav-item d-none d-sm-inline-block">
                <a href="#" class="nav-link">Contact</a>
            </li>
        </ul>

        <!-- Right navbar links -->
        <ul class="navbar-nav ml-auto">
            <!-- Navbar Search -->
            <li class="nav-item">
                <a class="nav-link" data-widget="navbar-search" href="#" role="button">
                    <i class="fas fa-search"></i>
                </a>
                <div class="navbar-search-block">
                    <form class="form-inline">
                        <div class="input-group input-group-sm">
                            <input class="form-control form-control-navbar" type="search" placeholder="Search" aria-label="Search">
                            <div class="input-group-append">
                                <button class="btn btn-navbar" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                                <button class="btn btn-navbar" type="button" data-widget="navbar-search">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </li>

            <!-- Messages Dropdown Menu -->
            <li class="nav-item dropdown">
                <a class="nav-link" data-toggle="dropdown" href="#">
                    <i class="far fa-comments"></i>
                    <span class="badge badge-danger navbar-badge">3</span>
                </a>
                <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                    <a href="#" class="dropdown-item">
                        <!-- Message Start -->
                        <div class="media">
                            <img src="<?= base_url('vendor/almasaeed2010/adminlte/dist/img/user1-128x128.jpg') ?>" alt="User Avatar" class="img-size-50 mr-3 img-circle">
                            <div class="media-body">
                                <h3 class="dropdown-item-title">
                                    Brad Diesel
                                    <span class="float-right text-sm text-danger"><i class="fas fa-star"></i></span>
                                </h3>
                                <p class="text-sm">Call me whenever you can...</p>
                                <p class="text-sm text-muted"><i class="far fa-clock mr-1"></i> 4 Hours Ago</p>
                            </div>
                        </div>
                        <!-- Message End -->
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item dropdown-footer">See All Messages</a>
                </div>
            </li>
            <!-- Notifications Dropdown Menu -->
            <li class="nav-item dropdown">
                <a class="nav-link" data-toggle="dropdown" href="#">
                    <i class="far fa-bell"></i>
                    <span class="badge badge-warning navbar-badge">15</span>
                </a>
                <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                    <span class="dropdown-item dropdown-header">15 Notifications</span>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-envelope mr-2"></i> 4 new messages
                        <span class="float-right text-muted text-sm">3 mins</span>
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item dropdown-footer">See All Notifications</a>
                </div>
            </li>

            <!-- User Menu -->
            <li class="nav-item dropdown">
                <a class="nav-link" data-toggle="dropdown" href="#">
                    <img src="<?= base_url('vendor/almasaeed2010/adminlte/dist/img/user2-160x160.jpg') ?>" alt="User Avatar" class="img-size-32 img-circle">
                </a>
                <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-user mr-2"></i> Profile
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-cog mr-2"></i> Settings
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-sign-out-alt mr-2"></i> Logout
                    </a>
                </div>
            </li>

            <li class="nav-item">
                <a class="nav-link" data-widget="fullscreen" href="#" role="button">
                    <i class="fas fa-expand-arrows-alt"></i>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-widget="control-sidebar" data-controlsidebar-slide="true" href="#" role="button">
                    <i class="fas fa-th-large"></i>
                </a>
            </li>
        </ul>
    </nav>
    <!-- /.navbar -->
