<!-- Settings Page -->
<div class="row">
    <div class="col-md-6">
        <!-- General Settings -->
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">General Settings</h3>
            </div>
            <form>
                <div class="card-body">
                    <div class="form-group">
                        <label for="siteName">Site Name</label>
                        <input type="text" class="form-control" id="siteName" value="AdminLTE 3" placeholder="Enter site name">
                    </div>
                    <div class="form-group">
                        <label for="siteDescription">Site Description</label>
                        <textarea class="form-control" id="siteDescription" rows="3" placeholder="Enter site description">A modern admin dashboard template</textarea>
                    </div>
                    <div class="form-group">
                        <label for="adminEmail">Admin Email</label>
                        <input type="email" class="form-control" id="adminEmail" value="<EMAIL>" placeholder="Enter admin email">
                    </div>
                    <div class="form-group">
                        <label for="timezone">Timezone</label>
                        <select class="form-control" id="timezone">
                            <option value="Asia/Jakarta" selected>Asia/Jakarta (WIB)</option>
                            <option value="Asia/Makassar">Asia/Makassar (WITA)</option>
                            <option value="Asia/Jayapura">Asia/Jayapura (WIT)</option>
                            <option value="UTC">UTC</option>
                        </select>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="maintenanceMode">
                        <label class="form-check-label" for="maintenanceMode">
                            Maintenance Mode
                        </label>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                    <button type="button" class="btn btn-secondary">Reset</button>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-md-6">
        <!-- Security Settings -->
        <div class="card card-warning">
            <div class="card-header">
                <h3 class="card-title">Security Settings</h3>
            </div>
            <form>
                <div class="card-body">
                    <div class="form-group">
                        <label for="sessionTimeout">Session Timeout (minutes)</label>
                        <input type="number" class="form-control" id="sessionTimeout" value="120" min="5" max="1440">
                    </div>
                    <div class="form-group">
                        <label for="maxLoginAttempts">Max Login Attempts</label>
                        <input type="number" class="form-control" id="maxLoginAttempts" value="5" min="1" max="10">
                    </div>
                    <div class="form-group">
                        <label for="passwordMinLength">Password Minimum Length</label>
                        <input type="number" class="form-control" id="passwordMinLength" value="8" min="6" max="20">
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="requireStrongPassword" checked>
                        <label class="form-check-label" for="requireStrongPassword">
                            Require Strong Password
                        </label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="enableTwoFactor">
                        <label class="form-check-label" for="enableTwoFactor">
                            Enable Two-Factor Authentication
                        </label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="logUserActivity" checked>
                        <label class="form-check-label" for="logUserActivity">
                            Log User Activity
                        </label>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-warning">Update Security</button>
                </div>
            </form>
        </div>
        
        <!-- System Info -->
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">System Information</h3>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>PHP Version</strong></td>
                        <td><?= phpversion() ?></td>
                    </tr>
                    <tr>
                        <td><strong>CodeIgniter Version</strong></td>
                        <td><?= CI_VERSION ?></td>
                    </tr>
                    <tr>
                        <td><strong>Server Software</strong></td>
                        <td><?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?></td>
                    </tr>
                    <tr>
                        <td><strong>Database</strong></td>
                        <td>MySQL 8.0</td>
                    </tr>
                    <tr>
                        <td><strong>Memory Limit</strong></td>
                        <td><?= ini_get('memory_limit') ?></td>
                    </tr>
                    <tr>
                        <td><strong>Upload Max Size</strong></td>
                        <td><?= ini_get('upload_max_filesize') ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Appearance Settings -->
<div class="row">
    <div class="col-12">
        <div class="card card-success">
            <div class="card-header">
                <h3 class="card-title">Appearance Settings</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Theme</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="theme" id="themeLight" value="light" checked>
                                <label class="form-check-label" for="themeLight">Light Theme</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="theme" id="themeDark" value="dark">
                                <label class="form-check-label" for="themeDark">Dark Theme</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Sidebar Color</label>
                            <select class="form-control" id="sidebarColor">
                                <option value="dark-primary" selected>Dark Primary</option>
                                <option value="dark-secondary">Dark Secondary</option>
                                <option value="dark-success">Dark Success</option>
                                <option value="dark-info">Dark Info</option>
                                <option value="dark-warning">Dark Warning</option>
                                <option value="dark-danger">Dark Danger</option>
                                <option value="light-primary">Light Primary</option>
                                <option value="light-secondary">Light Secondary</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Navbar Color</label>
                            <select class="form-control" id="navbarColor">
                                <option value="navbar-white" selected>White</option>
                                <option value="navbar-light">Light</option>
                                <option value="navbar-dark">Dark</option>
                                <option value="navbar-primary">Primary</option>
                                <option value="navbar-secondary">Secondary</option>
                                <option value="navbar-success">Success</option>
                                <option value="navbar-info">Info</option>
                                <option value="navbar-warning">Warning</option>
                                <option value="navbar-danger">Danger</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="sidebarMini">
                            <label class="form-check-label" for="sidebarMini">
                                Sidebar Mini Mode
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="layoutFixed" checked>
                            <label class="form-check-label" for="layoutFixed">
                                Fixed Layout
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <button type="button" class="btn btn-success">Apply Theme</button>
                <button type="button" class="btn btn-secondary">Reset to Default</button>
            </div>
        </div>
    </div>
</div>
