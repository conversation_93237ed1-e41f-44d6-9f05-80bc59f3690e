<!DOCTYPE html>
<html>
<head>
    <title>Test AdminLTE Layout</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .test-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>Test AdminLTE 3 Layout Setup</h1>
    
    <?php
    $base_path = '/var/www/html/med';
    $tests = array();
    
    // Test 1: Check if AdminLTE is installed via Composer
    $composer_file = $base_path . '/composer.json';
    if (file_exists($composer_file)) {
        $composer_content = file_get_contents($composer_file);
        if (strpos($composer_content, 'almasaeed2010/adminlte') !== false) {
            $tests[] = array('status' => 'success', 'message' => 'AdminLTE 3 terinstall via Composer');
        } else {
            $tests[] = array('status' => 'error', 'message' => 'AdminLTE 3 tidak ditemukan di composer.json');
        }
    } else {
        $tests[] = array('status' => 'error', 'message' => 'File composer.json tidak ditemukan');
    }
    
    // Test 2: Check vendor directory
    $vendor_adminlte = $base_path . '/vendor/almasaeed2010/adminlte';
    if (is_dir($vendor_adminlte)) {
        $tests[] = array('status' => 'success', 'message' => 'Direktori vendor AdminLTE ditemukan');
    } else {
        $tests[] = array('status' => 'error', 'message' => 'Direktori vendor AdminLTE tidak ditemukan');
    }
    
    // Test 3: Check layout files
    $layout_files = array(
        'application/views/layouts/header.php',
        'application/views/layouts/sidebar.php',
        'application/views/layouts/footer.php',
        'application/views/layouts/main.php'
    );
    
    foreach ($layout_files as $file) {
        $full_path = $base_path . '/' . $file;
        if (file_exists($full_path)) {
            $tests[] = array('status' => 'success', 'message' => "File layout $file ditemukan");
        } else {
            $tests[] = array('status' => 'error', 'message' => "File layout $file tidak ditemukan");
        }
    }
    
    // Test 4: Check base controller
    $base_controller = $base_path . '/application/core/MY_Controller.php';
    if (file_exists($base_controller)) {
        $tests[] = array('status' => 'success', 'message' => 'Base controller MY_Controller.php ditemukan');
    } else {
        $tests[] = array('status' => 'error', 'message' => 'Base controller MY_Controller.php tidak ditemukan');
    }
    
    // Test 5: Check example controllers
    $controllers = array(
        'application/controllers/Dashboard.php',
        'application/controllers/Users.php'
    );
    
    foreach ($controllers as $controller) {
        $full_path = $base_path . '/' . $controller;
        if (file_exists($full_path)) {
            $tests[] = array('status' => 'success', 'message' => "Controller $controller ditemukan");
        } else {
            $tests[] = array('status' => 'error', 'message' => "Controller $controller tidak ditemukan");
        }
    }
    
    // Test 6: Check example views
    $views = array(
        'application/views/dashboard.php',
        'application/views/users/index.php',
        'application/views/users/create.php',
        'application/views/users/edit.php'
    );
    
    foreach ($views as $view) {
        $full_path = $base_path . '/' . $view;
        if (file_exists($full_path)) {
            $tests[] = array('status' => 'success', 'message' => "View $view ditemukan");
        } else {
            $tests[] = array('status' => 'error', 'message' => "View $view tidak ditemukan");
        }
    }
    
    // Test 7: Check routes configuration
    $routes_file = $base_path . '/application/config/routes.php';
    if (file_exists($routes_file)) {
        $routes_content = file_get_contents($routes_file);
        if (strpos($routes_content, "default_controller'] = 'dashboard'") !== false) {
            $tests[] = array('status' => 'success', 'message' => 'Default controller sudah diset ke dashboard');
        } else {
            $tests[] = array('status' => 'info', 'message' => 'Default controller belum diset ke dashboard');
        }
    }
    
    // Display results
    $success_count = 0;
    $error_count = 0;
    
    foreach ($tests as $test) {
        echo '<div class="test-item ' . $test['status'] . '">';
        echo '<strong>' . strtoupper($test['status']) . ':</strong> ' . $test['message'];
        echo '</div>';
        
        if ($test['status'] == 'success') $success_count++;
        if ($test['status'] == 'error') $error_count++;
    }
    
    echo '<hr>';
    echo '<h2>Ringkasan</h2>';
    echo '<p><strong>Total Tests:</strong> ' . count($tests) . '</p>';
    echo '<p class="success"><strong>Berhasil:</strong> ' . $success_count . '</p>';
    echo '<p class="error"><strong>Error:</strong> ' . $error_count . '</p>';
    
    if ($error_count == 0) {
        echo '<div class="test-item success">';
        echo '<h3>🎉 Setup AdminLTE 3 Berhasil!</h3>';
        echo '<p>Semua file layout sudah tersedia. Anda bisa mengakses:</p>';
        echo '<ul>';
        echo '<li><a href="' . (isset($_SERVER['HTTP_HOST']) ? 'http://' . $_SERVER['HTTP_HOST'] . '/med' : '/med') . '">Dashboard</a></li>';
        echo '<li><a href="' . (isset($_SERVER['HTTP_HOST']) ? 'http://' . $_SERVER['HTTP_HOST'] . '/med/users' : '/med/users') . '">Users Management</a></li>';
        echo '</ul>';
        echo '</div>';
    } else {
        echo '<div class="test-item error">';
        echo '<h3>❌ Setup Belum Lengkap</h3>';
        echo '<p>Masih ada ' . $error_count . ' error yang perlu diperbaiki.</p>';
        echo '</div>';
    }
    ?>
    
    <hr>
    <h2>Langkah Selanjutnya</h2>
    <ol>
        <li>Pastikan semua file sudah ada (tidak ada error di atas)</li>
        <li>Jalankan <code>composer install</code> jika vendor belum ada</li>
        <li>Akses aplikasi melalui web browser</li>
        <li>Customize layout sesuai kebutuhan</li>
        <li>Baca dokumentasi di README_AdminLTE.md</li>
    </ol>
    
    <h2>Struktur File yang Dibuat</h2>
    <pre>
application/
├── controllers/
│   ├── Dashboard.php          # Controller dashboard
│   └── Users.php             # Controller users management
├── core/
│   └── MY_Controller.php     # Base controller
├── views/
│   ├── layouts/
│   │   ├── header.php        # Header dengan navbar
│   │   ├── sidebar.php       # Sidebar menu
│   │   ├── footer.php        # Footer dengan JS
│   │   └── main.php          # Layout utama
│   ├── users/
│   │   ├── index.php         # Daftar users
│   │   ├── create.php        # Form tambah user
│   │   └── edit.php          # Form edit user
│   └── dashboard.php         # Halaman dashboard
README_AdminLTE.md            # Dokumentasi penggunaan
    </pre>
</body>
</html>
