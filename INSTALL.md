# Panduan Instalasi AdminLTE 3 untuk CodeIgniter

## Persyaratan Sistem

- PHP 5.3.7 atau lebih tinggi
- Web server (Apache/Nginx)
- Composer
- MySQL/MariaDB (opsional)

## Langkah Instalasi

### 1. Clone atau Download Project

```bash
git clone <repository-url>
cd med
```

### 2. Install Dependencies

```bash
composer install
```

### 3. Konfigurasi Database (Opsional)

Edit file `application/config/database.php`:

```php
$db['default'] = array(
    'dsn'      => '',
    'hostname' => 'localhost',
    'username' => 'your_username',
    'password' => 'your_password',
    'database' => 'your_database',
    'dbdriver' => 'mysqli',
    'dbprefix' => '',
    'pconnect' => FALSE,
    'db_debug' => (ENVIRONMENT !== 'production'),
    'cache_on' => FALSE,
    'cachedir' => '',
    'char_set' => 'utf8',
    'dbcollat' => 'utf8_general_ci',
    'swap_pre' => '',
    'encrypt'  => FALSE,
    'compress' => FALSE,
    'stricton' => FALSE,
    'failover' => array(),
    'save_queries' => TRUE
);
```

### 4. Konfigurasi Base URL

Edit file `application/config/config.php`:

```php
$config['base_url'] = 'http://localhost/med/';
```

### 5. Set Permissions (Linux/Mac)

```bash
chmod -R 755 application/cache
chmod -R 755 application/logs
```

### 6. Test Installation

Buka browser dan akses:
- `http://localhost/med/` - Dashboard
- `http://localhost/med/users` - Users Management

Atau jalankan test script:

```bash
php test_layout.php
```

## Struktur Project

```
med/
├── application/
│   ├── controllers/
│   │   ├── Dashboard.php
│   │   └── Users.php
│   ├── core/
│   │   └── MY_Controller.php
│   ├── views/
│   │   ├── layouts/
│   │   │   ├── header.php
│   │   │   ├── sidebar.php
│   │   │   ├── footer.php
│   │   │   └── main.php
│   │   ├── users/
│   │   │   ├── index.php
│   │   │   ├── create.php
│   │   │   └── edit.php
│   │   └── dashboard.php
│   └── config/
├── assets/
│   ├── css/
│   │   └── custom.css
│   └── js/
│       └── custom.js
├── vendor/
│   └── almasaeed2010/
│       └── adminlte/
├── system/
├── .htaccess
├── index.php
├── composer.json
└── README_AdminLTE.md
```

## Konfigurasi Tambahan

### Session Configuration

Edit `application/config/config.php`:

```php
$config['sess_driver'] = 'files';
$config['sess_cookie_name'] = 'ci_session';
$config['sess_expiration'] = 7200;
$config['sess_save_path'] = NULL;
$config['sess_match_ip'] = FALSE;
$config['sess_time_to_update'] = 300;
$config['sess_regenerate_destroy'] = FALSE;
```

### Autoload Configuration

Edit `application/config/autoload.php`:

```php
$autoload['libraries'] = array('database', 'session');
$autoload['helper'] = array('url', 'form');
```

### Error Reporting

Untuk development, edit `index.php`:

```php
define('ENVIRONMENT', 'development');
```

Untuk production:

```php
define('ENVIRONMENT', 'production');
```

## Troubleshooting

### 1. CSS/JS tidak load

**Problem**: File CSS/JS AdminLTE tidak load

**Solution**: 
- Pastikan composer install sudah dijalankan
- Cek path di `base_url()` sudah benar
- Pastikan folder vendor/almasaeed2010/adminlte ada

### 2. 404 Error

**Problem**: Halaman menampilkan 404

**Solution**:
- Pastikan file .htaccess ada dan mod_rewrite aktif
- Cek konfigurasi base_url di config.php
- Pastikan controller dan method ada

### 3. Blank Page

**Problem**: Halaman kosong/blank

**Solution**:
- Cek error log di application/logs/
- Pastikan PHP error reporting aktif
- Cek syntax error di controller/view

### 4. Session Error

**Problem**: Session tidak berfungsi

**Solution**:
- Pastikan folder application/cache writable
- Cek konfigurasi session di config.php
- Pastikan session library di-autoload

### 5. Database Connection Error

**Problem**: Tidak bisa connect ke database

**Solution**:
- Cek konfigurasi database.php
- Pastikan MySQL service running
- Cek username/password database

## Tips Pengembangan

1. **Gunakan MY_Controller** sebagai base untuk semua controller
2. **Manfaatkan helper methods** di MY_Controller untuk konsistensi
3. **Pisahkan logic** antara controller, model, dan view
4. **Gunakan flash messages** untuk feedback user
5. **Implementasi validation** di controller
6. **Gunakan CSRF protection** untuk keamanan
7. **Optimize query** dengan menggunakan model
8. **Implement caching** untuk performa

## Keamanan

1. **Selalu validate input** dari user
2. **Gunakan prepared statements** untuk query database
3. **Implement CSRF protection**
4. **Set proper file permissions**
5. **Hide error messages** di production
6. **Use HTTPS** di production
7. **Regular security updates**

## Performance

1. **Enable caching** di production
2. **Optimize database queries**
3. **Compress CSS/JS files**
4. **Use CDN** untuk static files
5. **Enable gzip compression**
6. **Optimize images**

## Support

Untuk bantuan lebih lanjut:
1. Baca dokumentasi CodeIgniter: https://codeigniter.com/docs
2. Baca dokumentasi AdminLTE: https://adminlte.io/docs
3. Cek file README_AdminLTE.md untuk panduan penggunaan layout
