# AdminLTE 3 Layout untuk CodeIgniter

Layout AdminLTE 3 yang sudah terintegrasi dengan CodeIgniter menggunakan Composer.

## Struktur File

```
application/
├── controllers/
│   ├── Dashboard.php          # Contoh controller dashboard
│   └── Users.php             # Contoh controller users
├── core/
│   └── MY_Controller.php     # Base controller untuk AdminLTE
├── views/
│   ├── layouts/
│   │   ├── header.php        # Header dengan CSS dan navbar
│   │   ├── sidebar.php       # Sidebar menu
│   │   ├── footer.php        # Footer dengan JS
│   │   └── main.php          # Layout utama
│   ├── users/
│   │   ├── index.php         # Halaman daftar users
│   │   ├── create.php        # Form tambah user
│   │   └── edit.php          # Form edit user
│   └── dashboard.php         # Halaman dashboard
```

## Cara Penggunaan

### 1. Menggunakan Base Controller

Semua controller sebaiknya extend dari `MY_Controller`:

```php
<?php
class YourController extends MY_Controller {
    
    public function __construct()
    {
        parent::__construct();
    }
    
    public function index()
    {
        // Set page title
        $this->set_title('Page Title');
        
        // Set breadcrumbs
        $this->set_breadcrumbs(array(
            array('title' => 'Home', 'url' => base_url()),
            array('title' => 'Current Page')
        ));
        
        // Add CSS file
        $this->add_css(base_url('assets/css/custom.css'));
        
        // Add JS file
        $this->add_js(base_url('assets/js/custom.js'));
        
        // Add page script
        $this->add_script('console.log("Page loaded");');
        
        // Render view
        $this->render('your_view', $data);
    }
}
```

### 2. Method yang Tersedia di MY_Controller

#### set_title($title, $page_title = null)
Set judul halaman dan header konten.

#### set_breadcrumbs($breadcrumbs)
Set breadcrumb navigation.

#### add_css($css_file)
Tambah file CSS.

#### add_js($js_file)
Tambah file JavaScript.

#### add_script($script)
Tambah script JavaScript inline.

#### set_message($type, $message)
Set flash message. Type: success, error, warning, info.

#### redirect_to($uri)
Redirect ke URL dengan base_url.

#### render($view, $data = array(), $return = false)
Render view dengan layout AdminLTE.

### 3. Contoh Penggunaan Flash Messages

```php
// Set message
$this->set_message('success', 'Data berhasil disimpan!');
$this->set_message('error', 'Terjadi kesalahan!');
$this->set_message('warning', 'Peringatan!');
$this->set_message('info', 'Informasi penting.');

// Redirect
$this->redirect_to('users');
```

### 4. Struktur View

View Anda hanya perlu berisi konten utama, tanpa HTML wrapper:

```php
<!-- your_view.php -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Your Content</h3>
            </div>
            <div class="card-body">
                <!-- Your content here -->
            </div>
        </div>
    </div>
</div>
```

### 5. Kustomisasi Sidebar

Edit file `application/views/layouts/sidebar.php` untuk menambah/mengubah menu:

```php
<li class="nav-item">
    <a href="<?= base_url('your_controller') ?>" class="nav-link <?= $this->uri->segment(1) == 'your_controller' ? 'active' : '' ?>">
        <i class="nav-icon fas fa-your-icon"></i>
        <p>Your Menu</p>
    </a>
</li>
```

### 6. Menggunakan DataTables

```php
// Di controller
$this->add_css(base_url('vendor/almasaeed2010/adminlte/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css'));
$this->add_js(base_url('vendor/almasaeed2010/adminlte/plugins/datatables/jquery.dataTables.min.js'));
$this->add_js(base_url('vendor/almasaeed2010/adminlte/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js'));

$script = "
$(function () {
    $('#yourTable').DataTable({
        'responsive': true,
        'autoWidth': false,
    });
});
";
$this->add_script($script);
```

### 7. Menggunakan Form Validation

```php
// Di controller
$this->load->library('form_validation');

$this->form_validation->set_rules('name', 'Name', 'required');
$this->form_validation->set_rules('email', 'Email', 'required|valid_email');

if ($this->form_validation->run() == FALSE) {
    $this->set_message('error', validation_errors());
    $this->render('your_form');
} else {
    // Process form
    $this->set_message('success', 'Data berhasil disimpan!');
    $this->redirect_to('your_controller');
}
```

## Fitur AdminLTE yang Tersedia

- Responsive design
- Dark/Light mode
- Sidebar menu dengan submenu
- DataTables integration
- Chart.js integration
- Form components
- Card components
- Alert messages
- Modal dialogs
- Tabs dan accordions
- Progress bars
- Dan masih banyak lagi

## Customization

### Mengubah Tema
Edit file `application/views/layouts/header.php` untuk mengubah tema atau menambah CSS custom.

### Mengubah Logo
Edit file `application/views/layouts/sidebar.php` pada bagian brand-link.

### Menambah Plugin
Tambahkan CSS/JS plugin di controller menggunakan `add_css()` dan `add_js()`.

## Tips

1. Gunakan `MY_Controller` sebagai base untuk semua controller
2. Manfaatkan flash messages untuk feedback user
3. Gunakan breadcrumbs untuk navigasi yang jelas
4. Pisahkan CSS/JS custom per halaman menggunakan `add_css()` dan `add_js()`
5. Gunakan card component untuk wrapping konten
6. Manfaatkan grid system Bootstrap untuk layout responsive

## Troubleshooting

### CSS/JS tidak load
Pastikan path ke vendor AdminLTE benar dan file ada.

### Menu tidak active
Pastikan kondisi active di sidebar menggunakan `$this->uri->segment()` dengan benar.

### Flash message tidak muncul
Pastikan session library sudah di-load dan menggunakan `set_message()` sebelum redirect.
