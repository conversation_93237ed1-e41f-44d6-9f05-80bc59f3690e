# Tabbed IFrame Plugin untuk AdminLTE 3

Plugin ini memungkinkan membuka multiple halaman dalam tab dengan iframe, memberikan pengalaman seperti aplikasi desktop modern.

## Fitur

- ✅ **Multiple Tabs** - <PERSON>uka beberapa halaman dalam tab
- ✅ **IFrame Loading** - Setiap tab memuat halaman dalam iframe
- ✅ **Tab Management** - Tutup, refresh, dan navigasi antar tab
- ✅ **Keyboard Shortcuts** - Ctrl+W (tutup), Ctrl+R (refresh)
- ✅ **Auto Title Detection** - Otomatis mengambil title dari halaman
- ✅ **Loading Indicator** - Indikator loading saat memuat halaman
- ✅ **Error Handling** - Menangani error loading halaman
- ✅ **Responsive Design** - Mendukung tampilan mobile
- ✅ **Dark Mode Support** - Mendukung tema gelap
- ✅ **Max Tabs Limit** - <PERSON>asi jumlah maksimal tab

## Struktur File

```
assets/
├── css/
│   └── custom.css              # CSS untuk styling tab (sudah include di header.php)
└── js/
    └── tabbed-iframe.js        # JavaScript untuk functionality tab

application/
├── controllers/
│   └── Main.php               # Controller untuk halaman dengan tab
├── views/
│   └── layouts/
       └── main_tabbed.php     # Layout khusus untuk tab interface
```

## Cara Penggunaan

### 1. Akses Halaman dengan Tab

Buka browser dan akses:
```
http://localhost/med/
```

Halaman akan menampilkan interface dengan tab, dan tab pertama (Dashboard) akan terbuka otomatis.

### 2. Membuka Tab Baru

#### Melalui Sidebar Menu
Klik menu di sidebar untuk membuka halaman dalam tab baru. Contoh:
- Dashboard
- Users Management
- Add User
- Settings
- Reports

#### Melalui JavaScript
```javascript
// Buka tab baru
openTab('Page Title', 'http://example.com/page', 'optional-id');

// Contoh
openTab('Google', 'https://google.com');
openTab('Users', '/med/main/users', 'users-tab');
```

### 3. Mengelola Tab

#### Kontrol Tab
- **Refresh Tab**: Klik tombol refresh atau tekan Ctrl+R
- **Close Other Tabs**: Tutup semua tab kecuali yang aktif
- **Close All Tabs**: Tutup semua tab kecuali tab pertama

#### Keyboard Shortcuts
- `Ctrl + W` - Tutup tab aktif
- `Ctrl + R` - Refresh tab aktif
- `Ctrl + T` - (Reserved untuk future use)

#### Klik Tab
- **Klik tab** - Pindah ke tab tersebut
- **Klik × pada tab** - Tutup tab

### 4. Konfigurasi

#### Opsi Default
```javascript
const options = {
    container: '.content-wrapper',    // Container untuk tab
    maxTabs: 10,                     // Maksimal jumlah tab
    defaultTab: {                    // Tab default yang dibuka
        title: 'Dashboard',
        url: '/med/main/dashboard',
        id: 'dashboard'
    }
};

// Initialize dengan opsi custom
window.tabbedIFrame = new TabbedIFrame(options);
```

#### Mengubah Maksimal Tab
```javascript
// Set maksimal 15 tab
window.tabbedIFrame = new TabbedIFrame({
    maxTabs: 15
});
```

## API Reference

### Methods

#### addTab(title, url, id)
Menambah tab baru.
```javascript
const tab = tabbedIFrame.addTab('Page Title', '/path/to/page', 'optional-id');
```

#### closeTab(tabId)
Menutup tab berdasarkan ID.
```javascript
tabbedIFrame.closeTab('tab-id');
```

#### switchToTab(tabId)
Pindah ke tab tertentu.
```javascript
tabbedIFrame.switchToTab('tab-id');
```

#### refreshTab(tabId)
Refresh tab tertentu.
```javascript
tabbedIFrame.refreshTab('tab-id');
```

#### closeCurrentTab()
Tutup tab yang sedang aktif.
```javascript
tabbedIFrame.closeCurrentTab();
```

#### closeOtherTabs()
Tutup semua tab kecuali yang aktif.
```javascript
tabbedIFrame.closeOtherTabs();
```

#### closeAllTabs()
Tutup semua tab kecuali tab pertama.
```javascript
tabbedIFrame.closeAllTabs();
```

### Global Functions

#### openTab(title, url, id)
Function global untuk membuka tab dari mana saja.
```javascript
openTab('New Page', '/path/to/page');
```

#### closeTab(tabId)
Function global untuk menutup tab.
```javascript
closeTab('tab-id');
```

## Kustomisasi

### 1. Styling Tab

Edit CSS di `assets/css/custom.css` atau tambahkan CSS custom:

```css
/* Custom tab styling */
.tab-item {
    background: #your-color;
    border-color: #your-border-color;
}

.tab-item.active {
    background: #your-active-color;
}
```

### 2. Mengubah Behavior

Edit `assets/js/tabbed-iframe.js` untuk mengubah behavior:

```javascript
// Contoh: Auto-close tab setelah 30 detik tidak aktif
setTimeout(() => {
    if (tab.id !== this.activeTabId) {
        this.closeTab(tab.id);
    }
}, 30000);
```

### 3. Custom Event Handlers

```javascript
// Listen untuk event tab
document.addEventListener('tabOpened', function(e) {
    console.log('Tab opened:', e.detail);
});

document.addEventListener('tabClosed', function(e) {
    console.log('Tab closed:', e.detail);
});
```

## Integrasi dengan Sidebar

### 1. Menambah Menu Tab

Edit `application/views/layouts/sidebar.php`:

```php
<li class="nav-item">
    <a href="<?= base_url('your/page') ?>" class="nav-link" data-tab-title="Your Page Title">
        <i class="nav-icon fas fa-your-icon"></i>
        <p>Your Menu</p>
    </a>
</li>
```

### 2. Menu dengan JavaScript

```php
<li class="nav-item">
    <a href="#" class="nav-link" onclick="openTab('Page Title', '<?= base_url('your/page') ?>')">
        <i class="nav-icon fas fa-your-icon"></i>
        <p>Your Menu</p>
    </a>
</li>
```

## Controller untuk Tab Content

### 1. Extend MY_Controller

```php
<?php
class YourController extends MY_Controller {
    
    public function your_method()
    {
        $this->set_title('Page Title');
        $this->set_breadcrumbs(array(
            array('title' => 'Home', 'url' => base_url('main/dashboard')),
            array('title' => 'Your Page')
        ));
        
        // Render dengan layout biasa (untuk iframe)
        $this->render('your_view', $data);
    }
}
```

### 2. Breadcrumb untuk Tab

Gunakan link ke halaman tab untuk breadcrumb:
```php
$this->set_breadcrumbs(array(
    array('title' => 'Home', 'url' => base_url('main/dashboard')),
    array('title' => 'Parent Page', 'url' => base_url('main/parent')),
    array('title' => 'Current Page')
));
```

## Troubleshooting

### 1. Tab tidak terbuka
- Pastikan URL valid dan dapat diakses
- Cek console browser untuk error JavaScript
- Pastikan `tabbed-iframe.js` sudah di-load

### 2. Iframe tidak load
- Cek apakah halaman target mendukung iframe
- Beberapa situs memblokir iframe (X-Frame-Options)
- Pastikan tidak ada error CORS

### 3. CSS tidak sesuai
- Pastikan CSS tab sudah di-include di header
- Cek konflik dengan CSS lain
- Gunakan browser developer tools untuk debug

### 4. Memory usage tinggi
- Tutup tab yang tidak digunakan
- Kurangi `maxTabs` limit
- Implement auto-close untuk tab tidak aktif

## Tips & Best Practices

1. **Gunakan data-tab-title** untuk title yang lebih deskriptif
2. **Batasi jumlah tab** untuk performa yang baik
3. **Tutup tab yang tidak digunakan** secara berkala
4. **Gunakan breadcrumb** yang konsisten dalam iframe
5. **Test di berbagai browser** untuk kompatibilitas
6. **Implement error handling** untuk halaman yang gagal load
7. **Gunakan loading indicator** untuk UX yang baik

## Browser Support

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ⚠️ IE 11 (limited support)

## Performance

- **Memory Usage**: ~2-5MB per tab
- **Load Time**: Tergantung konten iframe
- **Recommended Max Tabs**: 10-15 tab
- **Mobile Performance**: Optimized untuk mobile
